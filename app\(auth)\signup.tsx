import { useRouter } from "expo-router";
import React, { useState } from "react";
import { Alert, Dimensions, Text, TextInput, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { ChevronDown } from "~/lib/icons/ChevronDown";
import { supabase } from "~/lib/supabase";
import { useColorScheme } from "~/lib/useColorScheme";
import { setUserRole, type UserRole } from "~/lib/utils";

const { height } = Dimensions.get("window");

export default function Signup() {
  const router = useRouter();
  const { isDarkColorScheme } = useColorScheme();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [selectedRole, setSelectedRole] = useState<UserRole>("student");
  const [loading, setLoading] = useState(false);
  const [showOtpForm, setShowOtpForm] = useState(false);
  const [otp, setOtp] = useState("");
  const [verifyingOtp, setVerifyingOtp] = useState(false);

  const roles: { value: UserRole; label: string }[] = [
    { value: "student", label: "Student" },
    { value: "teacher", label: "Teacher" },
    { value: "HOD", label: "HOD" },
  ];

  async function signUpWithEmail() {
    if (!email || !password || !confirmPassword) {
      Alert.alert("Error", "Please fill in all fields");
      return;
    }

    if (password !== confirmPassword) {
      Alert.alert("Error", "Passwords do not match");
      return;
    }

    if (password.length < 6) {
      Alert.alert("Error", "Password must be at least 6 characters");
      return;
    }

    setLoading(true);
    try {
      const { error } = await supabase.auth.signUp({
        email: email,
        password: password,
      });

      if (error) {
        Alert.alert("Error", error.message);
      } else {
        // Store user role in AsyncStorage
        await setUserRole(selectedRole);
        
        // Show OTP verification form
        setShowOtpForm(true);
        Alert.alert("Success", "Account created! Please check your email for the verification code.");
      }
    } catch (err) {
      console.error("Signup error:", err);
      Alert.alert("Error", "An unexpected error occurred");
    } finally {
      setLoading(false);
    }
  }

  async function verifyOTP() {
    if (!otp || otp.length !== 6) {
      Alert.alert("Error", "Please enter a valid 6-digit code");
      return;
    }

    setVerifyingOtp(true);
    try {
      const { data, error } = await supabase.auth.verifyOtp({
        email: email,
        token: otp,
        type: "signup",
      });

      if (error) {
        Alert.alert("Error", error.message);
      } else if (data.user) {
        Alert.alert("Success", "Email verified successfully!", [
          {
            text: "Continue",
            onPress: () => {
              // Navigate to the appropriate screen based on role
              router.replace("/(tabs)/student");
            },
          },
        ]);
      }
    } catch (err) {
      console.error("OTP verification error:", err);
      Alert.alert("Error", "An unexpected error occurred");
    } finally {
      setVerifyingOtp(false);
    }
  }

  async function resendOTP() {
    try {
      const { error } = await supabase.auth.resend({
        type: "signup",
        email: email,
      });

      if (error) {
        Alert.alert("Error", error.message);
      } else {
        Alert.alert("Success", "Verification code resent to your email!");
      }
    } catch (err) {
      console.error("Resend OTP error:", err);
      Alert.alert("Error", "An unexpected error occurred");
    }
  }

  if (showOtpForm) {
    return (
      <SafeAreaView 
        className={`flex-1 ${isDarkColorScheme ? 'bg-gray-900' : 'bg-white'}`}
        style={{ paddingTop: height * 0.05 }}
      >
        <View className="flex-1 px-8">
          {/* Header */}
          <View className="flex-row items-center mb-12">
            <TouchableOpacity
              onPress={() => setShowOtpForm(false)}
              className={`w-10 h-10 rounded-full items-center justify-center ${
                isDarkColorScheme ? 'bg-gray-800' : 'bg-gray-100'
              }`}
            >
              <Text className={`text-xl ${isDarkColorScheme ? 'text-white' : 'text-gray-900'}`}>
                ←
              </Text>
            </TouchableOpacity>
            <Text className={`text-2xl font-bold ml-4 ${
              isDarkColorScheme ? 'text-white' : 'text-gray-900'
            }`}>
              Verify Email
            </Text>
          </View>

          {/* OTP Form */}
          <View className="flex-1">
            <View className="mb-8">
              <Text className={`text-lg font-medium mb-2 ${
                isDarkColorScheme ? 'text-white' : 'text-gray-900'
              }`}>
                Verification Code
              </Text>
              <Text className={`text-base mb-4 ${
                isDarkColorScheme ? 'text-gray-400' : 'text-gray-600'
              }`}>
                We&apos;ve sent a 6-digit verification code to {email}
              </Text>
              <TextInput
                value={otp}
                onChangeText={setOtp}
                placeholder="Enter 6-digit code"
                placeholderTextColor={isDarkColorScheme ? '#9CA3AF' : '#6B7280'}
                className={`w-full py-4 px-4 rounded-xl text-lg text-center tracking-widest ${
                  isDarkColorScheme 
                    ? 'bg-gray-800 text-white border border-gray-700' 
                    : 'bg-gray-50 text-gray-900 border border-gray-200'
                }`}
                keyboardType="numeric"
                maxLength={6}
                autoFocus
              />
            </View>

            {/* Verify Button */}
            <TouchableOpacity
              onPress={verifyOTP}
              disabled={verifyingOtp || otp.length !== 6}
              className={`w-full py-4 rounded-xl items-center mb-6 ${
                verifyingOtp || otp.length !== 6
                  ? (isDarkColorScheme ? 'bg-gray-700' : 'bg-gray-300')
                  : (isDarkColorScheme ? 'bg-blue-600' : 'bg-blue-500')
              }`}
              activeOpacity={0.8}
            >
              <Text className="text-white font-semibold text-lg">
                {verifyingOtp ? "Verifying..." : "Verify Code"}
              </Text>
            </TouchableOpacity>

            {/* Resend Code */}
            <TouchableOpacity 
              onPress={resendOTP}
              className="w-full py-4 rounded-xl items-center"
            >
              <Text className={`text-base font-semibold ${
                isDarkColorScheme ? 'text-blue-400' : 'text-blue-600'
              }`}>
                Resend Code
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView 
      className={`flex-1 ${isDarkColorScheme ? 'bg-gray-900' : 'bg-white'}`}
      style={{ paddingTop: height * 0.05 }}
    >
      <View className="flex-1 px-8">
        {/* Header */}
        <View className="flex-row items-center mb-12">
          <TouchableOpacity
            onPress={() => router.back()}
            className={`w-10 h-10 rounded-full items-center justify-center ${
              isDarkColorScheme ? 'bg-gray-800' : 'bg-gray-100'
            }`}
          >
            <Text className={`text-xl ${isDarkColorScheme ? 'text-white' : 'text-gray-900'}`}>
              ←
            </Text>
          </TouchableOpacity>
          <Text className={`text-2xl font-bold ml-4 ${
            isDarkColorScheme ? 'text-white' : 'text-gray-900'
          }`}>
            Create Account
          </Text>
        </View>

        {/* Form */}
        <View className="flex-1">
          <View className="mb-8">
            <Text className={`text-lg font-medium mb-2 ${
              isDarkColorScheme ? 'text-white' : 'text-gray-900'
            }`}>
              Email
            </Text>
            <TextInput
              value={email}
              onChangeText={setEmail}
              placeholder="Enter your email"
              placeholderTextColor={isDarkColorScheme ? '#9CA3AF' : '#6B7280'}
              className={`w-full py-4 px-4 rounded-xl text-lg ${
                isDarkColorScheme 
                  ? 'bg-gray-800 text-white border border-gray-700' 
                  : 'bg-gray-50 text-gray-900 border border-gray-200'
              }`}
              autoCapitalize="none"
              keyboardType="email-address"
            />
          </View>

          <View className="mb-8">
            <Text className={`text-lg font-medium mb-2 ${
              isDarkColorScheme ? 'text-white' : 'text-gray-900'
            }`}>
              Password
            </Text>
            <TextInput
              value={password}
              onChangeText={setPassword}
              placeholder="Create a password"
              placeholderTextColor={isDarkColorScheme ? '#9CA3AF' : '#6B7280'}
              className={`w-full py-4 px-4 rounded-xl text-lg ${
                isDarkColorScheme 
                  ? 'bg-gray-800 text-white border border-gray-700' 
                  : 'bg-gray-50 text-gray-900 border border-gray-200'
              }`}
              secureTextEntry
              autoCapitalize="none"
            />
            <Text className={`text-sm mt-2 ${
              isDarkColorScheme ? 'text-gray-400' : 'text-gray-500'
            }`}>
              Must be at least 6 characters
            </Text>
          </View>

          <View className="mb-8">
            <Text className={`text-lg font-medium mb-2 ${
              isDarkColorScheme ? 'text-white' : 'text-gray-900'
            }`}>
              Confirm Password
            </Text>
            <TextInput
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              placeholder="Confirm your password"
              placeholderTextColor={isDarkColorScheme ? '#9CA3AF' : '#6B7280'}
              className={`w-full py-4 px-4 rounded-xl text-lg ${
                isDarkColorScheme 
                  ? 'bg-gray-800 text-white border border-gray-700' 
                  : 'bg-gray-50 text-gray-900 border border-gray-200'
              }`}
              secureTextEntry
              autoCapitalize="none"
            />
          </View>

          <View className="mb-8">
            <Text className={`text-lg font-medium mb-2 ${
              isDarkColorScheme ? 'text-white' : 'text-gray-900'
            }`}>
              Role
            </Text>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <TouchableOpacity
                  className={`w-full py-4 px-4 rounded-xl flex-row items-center justify-between ${
                    isDarkColorScheme 
                      ? 'bg-gray-800 border border-gray-700' 
                      : 'bg-gray-50 border border-gray-200'
                  }`}
                >
                  <Text className={`text-lg ${
                    isDarkColorScheme ? 'text-white' : 'text-gray-900'
                  }`}>
                    {roles.find(role => role.value === selectedRole)?.label}
                  </Text>
                  <ChevronDown 
                    size={20} 
                    className={isDarkColorScheme ? 'text-gray-400' : 'text-gray-500'} 
                  />
                </TouchableOpacity>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                {roles.map((role) => (
                  <DropdownMenuItem
                    key={role.value}
                    onPress={() => setSelectedRole(role.value)}
                    className={`py-3 ${
                      selectedRole === role.value ? 'bg-blue-50' : ''
                    }`}
                  >
                    <Text className={`text-lg ${
                      selectedRole === role.value ? 'text-blue-600 font-semibold' : 'text-gray-900'
                    }`}>
                      {role.label}
                    </Text>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </View>

          {/* Sign Up Button */}
          <TouchableOpacity
            onPress={signUpWithEmail}
            disabled={loading}
            className={`w-full py-4 rounded-xl items-center mb-6 ${
              loading 
                ? (isDarkColorScheme ? 'bg-gray-700' : 'bg-gray-300')
                : (isDarkColorScheme ? 'bg-blue-600' : 'bg-blue-500')
            }`}
            activeOpacity={0.8}
          >
            <Text className="text-white font-semibold text-lg">
              {loading ? "Creating Account..." : "Create Account"}
            </Text>
          </TouchableOpacity>

          {/* Terms */}
          <Text className={`text-sm text-center leading-5 mb-8 ${
            isDarkColorScheme ? 'text-gray-400' : 'text-gray-500'
          }`}>
            By creating an account, you agree to our{" "}
            <Text className={`font-semibold ${
              isDarkColorScheme ? 'text-blue-400' : 'text-blue-600'
            }`}>
              Terms of Service
            </Text>{" "}
            and{" "}
            <Text className={`font-semibold ${
              isDarkColorScheme ? 'text-blue-400' : 'text-blue-600'
            }`}>
              Privacy Policy
            </Text>
          </Text>

          {/* Divider */}
          <View className="flex-row items-center mb-8">
            <View className={`flex-1 h-px ${
              isDarkColorScheme ? 'bg-gray-700' : 'bg-gray-300'
            }`} />
            <Text className={`mx-4 text-sm ${
              isDarkColorScheme ? 'text-gray-400' : 'text-gray-500'
            }`}>
              or
            </Text>
            <View className={`flex-1 h-px ${
              isDarkColorScheme ? 'bg-gray-700' : 'bg-gray-300'
            }`} />
          </View>

          {/* Sign In Link */}
          <View className="flex-row justify-center">
            <Text className={`text-base ${
              isDarkColorScheme ? 'text-gray-400' : 'text-gray-500'
            }`}>
              Already have an account?{" "}
            </Text>
            <TouchableOpacity onPress={() => router.push("/(auth)/login")}>
              <Text className={`text-base font-semibold ${
                isDarkColorScheme ? 'text-blue-400' : 'text-blue-600'
              }`}>
                Sign In
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
}

