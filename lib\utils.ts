import AsyncStorage from "@react-native-async-storage/async-storage";
import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export type UserRole = "student" | "teacher" | "HOD";

// User role management
export async function getUserRole(): Promise<UserRole | null> {
  try {
    const role = await AsyncStorage.getItem("userRole");
    return role as UserRole | null;
  } catch (error) {
    console.error("Error getting user role:", error);
    return null;
  }
}

export async function setUserRole(role: UserRole): Promise<void> {
  try {
    await AsyncStorage.setItem("userRole", role);
  } catch (error) {
    console.error("Error setting user role:", error);
  }
}

export async function clearUserRole(): Promise<void> {
  try {
    await AsyncStorage.removeItem("userRole");
  } catch (error) {
    console.error("Error clearing user role:", error);
  }
}
