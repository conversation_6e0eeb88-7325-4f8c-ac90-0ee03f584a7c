import { useRouter } from "expo-router";
import React, { useState } from "react";
import { Alert, Dimensions, Text, TextInput, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { supabase } from "~/lib/supabase";
import { useColorScheme } from "~/lib/useColorScheme";

const { height } = Dimensions.get("window");

export default function Login() {
  const router = useRouter();
  const { isDarkColorScheme } = useColorScheme();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);

  async function signInWithEmail() {
    if (!email || !password) {
      Alert.alert("Error", "Please fill in all fields");
      return;
    }

    setLoading(true);
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: email,
        password: password,
      });

      if (error) {
        Alert.alert("Error", error.message);
      } else if (data.user) {
        // Redirect to tabs after successful login
        router.replace("/(tabs)/student");
      }
    } catch (err) {
      console.error("Login error:", err);
      Alert.alert("Error", "An unexpected error occurred");
    } finally {
      setLoading(false);
    }
  }

  return (
    <SafeAreaView 
      className={`flex-1 ${isDarkColorScheme ? 'bg-gray-900' : 'bg-white'}`}
      style={{ paddingTop: height * 0.05 }}
    >
      <View className="flex-1 px-8">
        {/* Header */}
        <View className="flex-row items-center mb-12">
          <TouchableOpacity
            onPress={() => router.back()}
            className={`w-10 h-10 rounded-full items-center justify-center ${
              isDarkColorScheme ? 'bg-gray-800' : 'bg-gray-100'
            }`}
          >
            <Text className={`text-xl ${isDarkColorScheme ? 'text-white' : 'text-gray-900'}`}>
              ←
            </Text>
          </TouchableOpacity>
          <Text className={`text-2xl font-bold ml-4 ${
            isDarkColorScheme ? 'text-white' : 'text-gray-900'
          }`}>
            Sign In
          </Text>
        </View>

        {/* Form */}
        <View className="flex-1">
          <View className="mb-8">
            <Text className={`text-lg font-medium mb-2 ${
              isDarkColorScheme ? 'text-white' : 'text-gray-900'
            }`}>
              Email
            </Text>
            <TextInput
              value={email}
              onChangeText={setEmail}
              placeholder="Enter your email"
              placeholderTextColor={isDarkColorScheme ? '#9CA3AF' : '#6B7280'}
              className={`w-full py-4 px-4 rounded-xl text-lg ${
                isDarkColorScheme 
                  ? 'bg-gray-800 text-white border border-gray-700' 
                  : 'bg-gray-50 text-gray-900 border border-gray-200'
              }`}
              autoCapitalize="none"
              keyboardType="email-address"
            />
          </View>

          <View className="mb-8">
            <Text className={`text-lg font-medium mb-2 ${
              isDarkColorScheme ? 'text-white' : 'text-gray-900'
            }`}>
              Password
            </Text>
            <TextInput
              value={password}
              onChangeText={setPassword}
              placeholder="Enter your password"
              placeholderTextColor={isDarkColorScheme ? '#9CA3AF' : '#6B7280'}
              className={`w-full py-4 px-4 rounded-xl text-lg ${
                isDarkColorScheme 
                  ? 'bg-gray-800 text-white border border-gray-700' 
                  : 'bg-gray-50 text-gray-900 border border-gray-200'
              }`}
              secureTextEntry
              autoCapitalize="none"
            />
          </View>

          {/* Sign In Button */}
          <TouchableOpacity
            onPress={signInWithEmail}
            disabled={loading}
            className={`w-full py-4 rounded-xl items-center mb-6 ${
              loading 
                ? (isDarkColorScheme ? 'bg-gray-700' : 'bg-gray-300')
                : (isDarkColorScheme ? 'bg-blue-600' : 'bg-blue-500')
            }`}
            activeOpacity={0.8}
          >
            <Text className="text-white font-semibold text-lg">
              {loading ? "Signing In..." : "Sign In"}
            </Text>
          </TouchableOpacity>

          {/* Forgot Password */}
          <TouchableOpacity className="items-center mb-8">
            <Text className={`text-base ${
              isDarkColorScheme ? 'text-blue-400' : 'text-blue-600'
            }`}>
              Forgot Password?
            </Text>
          </TouchableOpacity>

          {/* Divider */}
          <View className="flex-row items-center mb-8">
            <View className={`flex-1 h-px ${
              isDarkColorScheme ? 'bg-gray-700' : 'bg-gray-300'
            }`} />
            <Text className={`mx-4 text-sm ${
              isDarkColorScheme ? 'text-gray-400' : 'text-gray-500'
            }`}>
              or
            </Text>
            <View className={`flex-1 h-px ${
              isDarkColorScheme ? 'bg-gray-700' : 'bg-gray-300'
            }`} />
          </View>

          {/* Sign Up Link */}
          <View className="flex-row justify-center">
            <Text className={`text-base ${
              isDarkColorScheme ? 'text-gray-400' : 'text-gray-500'
            }`}>
              Don&apos;t have an account?{" "}
            </Text>
            <TouchableOpacity onPress={() => router.push("/(auth)/signup")}>
              <Text className={`text-base font-semibold ${
                isDarkColorScheme ? 'text-blue-400' : 'text-blue-600'
              }`}>
                Sign Up
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
}
