import { Redirect } from "expo-router";
import { useEffect, useState } from "react";
import { View, Text } from "react-native";
import { getUserRole, type UserRole } from "~/lib/utils";
import { useColorScheme } from "~/lib/useColorScheme";

export default function TabsIndex() {
  const [userRole, setUserRole] = useState<UserRole | null>(null);
  const [loading, setLoading] = useState(true);
  const { isDarkColorScheme } = useColorScheme();

  useEffect(() => {
    async function loadUserRole() {
      try {
        const role = await getUserRole();
        setUserRole(role);
      } catch (error) {
        console.error("Error loading user role:", error);
      } finally {
        setLoading(false);
      }
    }

    loadUserRole();
  }, []);

  if (loading) {
    return (
      <View className={`flex-1 items-center justify-center ${
        isDarkColorScheme ? 'bg-gray-900' : 'bg-white'
      }`}>
        <Text className={`text-lg ${
          isDarkColorScheme ? 'text-white' : 'text-gray-900'
        }`}>
          Loading...
        </Text>
      </View>
    );
  }

  // Redirect to role-specific screen
  switch (userRole) {
    case "student":
      return <Redirect href="/(tabs)/student" />;
    case "teacher":
      return <Redirect href="/(tabs)/teacher" />;
    case "HOD":
      return <Redirect href="/(tabs)/hod" />;
    default:
      return (
        <View className={`flex-1 items-center justify-center ${
          isDarkColorScheme ? 'bg-gray-900' : 'bg-white'
        }`}>
          <Text className={`text-lg ${
            isDarkColorScheme ? 'text-white' : 'text-gray-900'
          }`}>
            Invalid role. Please sign in again.
          </Text>
        </View>
      );
  }
}
