import { Session } from "@supabase/supabase-js";
import { useRouter } from "expo-router";
import { useEffect, useState } from "react";
import { View, Text } from "react-native";
import { supabase } from "~/lib/supabase";
import { useColorScheme } from "~/lib/useColorScheme";

export default function App() {
  const [session, setSession] = useState<Session | null>(null);
  const router = useRouter();
  const { isDarkColorScheme } = useColorScheme();

  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
    });

    supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
    });
  }, []);

  // If no session, redirect to auth welcome screen
  if (!session || !session.user) {
    router.replace("/(auth)/welcome");
    return null;
  }

  // If authenticated, redirect to tabs
  router.replace("/(tabs)");
  
  return (
    <View className={`flex-1 items-center justify-center ${
      isDarkColorScheme ? 'bg-gray-900' : 'bg-white'
    }`}>
      <Text className={`text-lg ${
        isDarkColorScheme ? 'text-white' : 'text-gray-900'
      }`}>
        Redirecting...
      </Text>
    </View>
  );
}
