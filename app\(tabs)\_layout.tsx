import { Tabs } from "expo-router";
import { useEffect, useState } from "react";
import { View, Text } from "react-native";
import { getUserRole, type UserRole } from "~/lib/utils";
import { useColorScheme } from "~/lib/useColorScheme";

export default function TabsLayout() {
  const [userRole, setUserRole] = useState<UserRole | null>(null);
  const [loading, setLoading] = useState(true);
  const { isDarkColorScheme } = useColorScheme();

  useEffect(() => {
    async function loadUserRole() {
      try {
        const role = await getUserRole();
        setUserRole(role);
      } catch (error) {
        console.error("Error loading user role:", error);
      } finally {
        setLoading(false);
      }
    }

    loadUserRole();
  }, []);

  if (loading) {
    return (
      <View className={`flex-1 items-center justify-center ${
        isDarkColorScheme ? 'bg-gray-900' : 'bg-white'
      }`}>
        <Text className={`text-lg ${
          isDarkColorScheme ? 'text-white' : 'text-gray-900'
        }`}>
          Loading...
        </Text>
      </View>
    );
  }

  if (!userRole) {
    return (
      <View className={`flex-1 items-center justify-center ${
        isDarkColorScheme ? 'bg-gray-900' : 'bg-white'
      }`}>
        <Text className={`text-lg ${
          isDarkColorScheme ? 'text-white' : 'text-gray-900'
        }`}>
          No role found. Please sign in again.
        </Text>
      </View>
    );
  }

  // Render tabs with role-specific styling
  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        tabBarStyle: {
          backgroundColor: isDarkColorScheme ? '#1f2937' : '#ffffff',
          borderTopColor: isDarkColorScheme ? '#374151' : '#e5e7eb',
        },
        tabBarActiveTintColor: isDarkColorScheme ? '#60a5fa' : '#3b82f6',
        tabBarInactiveTintColor: isDarkColorScheme ? '#9ca3af' : '#6b7280',
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: "Dashboard",
          tabBarIcon: ({ color, size }) => (
            <Text style={{ color, fontSize: size }}>🏠</Text>
          ),
        }}
      />
      <Tabs.Screen
        name="student"
        options={{
          title: "Student",
          tabBarIcon: ({ color, size }) => (
            <Text style={{ color, fontSize: size }}>🎓</Text>
          ),
        }}
      />
      <Tabs.Screen
        name="teacher"
        options={{
          title: "Teacher",
          tabBarIcon: ({ color, size }) => (
            <Text style={{ color, fontSize: size }}>👨‍🏫</Text>
          ),
        }}
      />
      <Tabs.Screen
        name="hod"
        options={{
          title: "HOD",
          tabBarIcon: ({ color, size }) => (
            <Text style={{ color, fontSize: size }}>👔</Text>
          ),
        }}
      />
    </Tabs>
  );
}
